<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="prototype-page-id" content="landForm"> <!-- 添加此行指定当前页面ID -->
    <script src="annotationsData.js" defer></script>
    <script src="prototypeAnnotations.js" defer></script>

    <title>土地信息表单</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://demo.axureux.com/fontawesome/5.7.2/pro/css/all.min.css">
    <!-- 引入ElementUI组件库 -->
    <link rel="stylesheet" href="assets/element-ui/index.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --font-size-base: 14px;
            --heading-color: rgba(0, 0, 0, 0.85);
            --text-color: rgba(0, 0, 0, 0.65);
            --disabled-color: rgba(0, 0, 0, 0.25);
            --border-color-base: #d9d9d9;
            --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: var(--font-size-base);
            color: var(--text-color);
            background-color: #f5f7fa;
            line-height: 1.5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }

        .page-header {
            margin-bottom: 20px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color-base);
        }

        .page-title {
            font-size: 24px;
            color: var(--heading-color);
            font-weight: 500;
        }

        .form-card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: var(--box-shadow-base);
            margin-bottom: 24px;
        }

        .form-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-base);
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
        }

        .form-card-title {
            font-size: 16px;
            color: var(--heading-color);
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .form-card-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .form-card-body {
            padding: 24px;
        }

        .form-footer {
            text-align: center;
            padding: 24px 0;
        }

        .required:before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }

        /* 自定义ElementUI样式 */
        .el-form-item {
            margin-bottom: 22px;
        }

        .el-textarea__inner {
            min-height: 120px !important;
        }

        .el-form-item__label {
            font-weight: 500;
        }

        .el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .el-button--primary:hover,
        .el-button--primary:focus {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .el-date-editor.el-input {
            width: 100%;
        }

        .el-select {
            width: 100%;
        }

        .el-input-number {
            width: 100%;
        }

        .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
        }

        .el-checkbox {
            margin-right: 15px;
            margin-bottom: 5px;
        }

        /* 提示图标样式 */
        .tooltip-icon {
            color: #909399;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 去除Vue初始化闪烁 */
        [v-cloak] {
            display: none;
        }

        /* 帮助文本样式 */
        .help-text {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 5px;
        }
        
        .record-table-form-item {
            margin-bottom: 0;
        }

        .record-table-form-item .el-form-item__content {
            margin-left: 0 !important;
        }
        
        .record-table-form-item .el-form-item__error {
            position: absolute;
            z-index: 1;
            top: 100%;
            left: 0;
        }

        .empty-text {
            color: #909399;
            text-align: center;
            padding: 20px 0;
        }

        /* 级联选择器样式 */
        .el-cascader {
            width: 100%;
        }

        /* 标签区分 */
        .form-section-title {
            font-size: 15px;
            color: var(--heading-color);
            font-weight: 500;
            margin: 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px dashed #e8e8e8;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">土地信息表单</h1>
            </div>

            <el-form :model="formData" :rules="rules" ref="landForm" label-width="150px" size="small">
                <!-- 基本信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="code">
                                    <template slot="label">
                                        <span>资产编号</span>
                                    </template>
                                    <el-input v-model="formData.code" placeholder="保存后系统自动生成" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="enterpriseCode">
                                    <template slot="label">
                                        <span>企业自定义编号</span>
                                    </template>
                                    <el-input v-model="formData.enterpriseCode" placeholder="请输入企业自定义编号"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="name">
                                    <template slot="label">
                                        <span>资产项目（资产名称）</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('系统内要求资产名称唯一')"></i>
                                    </template>
                                    <el-input v-model="formData.name" placeholder="请输入资产项目（资产名称）"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="groupName">
                                    <template slot="label">
                                        <span>所属集团</span>
                                    </template>
                                    <el-select v-model="formData.groupName" placeholder="请选择所属集团" disabled>
                                        <el-option :key="0" label="厦门市城市建设发展投资有限公司" :value="0"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="companyName">
                                    <template slot="label">
                                        <span>所属企业</span>
                                    </template>
                                    <el-select v-model="formData.companyName" placeholder="请选择所属企业" @change="handleCompanyChange">
                                        <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="ownUnit">
                                    <template slot="label">
                                        <span id="ownUnit">权属单位名称</span>
                                    </template>
                                    <el-input v-model="formData.ownUnit" placeholder="请输入权属单位名称"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="region">
                                    <template slot="label">
                                        <span>资产位置</span>
                                    </template>
                                    <el-cascader
                                        v-model="formData.region"
                                        :options="regionOptions"
                                        @change="handleRegionChange"
                                        placeholder="请选择省/市/区">
                                    </el-cascader>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="address">
                                    <template slot="label">
                                        <span>详细地址</span>
                                    </template>
                                    <el-input v-model="formData.address" placeholder="请输入详细地址"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="status" @change="handleStatusChange">
                                    <template slot="label">
                                        <span id="status">状态</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('备案数据支持撤回、草稿数据和撤回数据支持作废')"></i>
                                    </template>
                                    <el-select v-model="formData.status" placeholder="请选择状态">
                                        <el-option 
                                            v-for="item in statusOptions" 
                                            :key="item.value" 
                                            :label="item.label" 
                                            :value="item.value" 
                                            :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="管理单位" prop="manageUnit">
                                    <el-select v-model="formData.manageUnit" placeholder="请选择管理单位">
                                        <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否报送国资委" prop="reportOrNot">
                                    <el-select v-model="formData.reportOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="经办人" prop="operator">
                                    <el-input v-model="formData.operator" placeholder="请输入经办人"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="录入人" prop="entryClerk" required>
                                    <el-input v-model="formData.entryClerk" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="录入时间" prop="createTime" required>
                                    <el-input v-model="formData.createTime" disabled></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                
                <!-- 土地信息 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-map-marked-alt"></i> 土地信息
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="useType">
                                    <template slot="label">
                                        <span>土地用途（运营方式和情况）</span>
                                    </template>
                                    <el-select v-model="formData.useType" placeholder="请选择土地用途（运营方式和情况）" @change="handleUseTypeChange">
                                        <el-option :key="0" label="居住" :value="0"></el-option>
                                        <el-option :key="1" label="商业" :value="1"></el-option>
                                        <el-option :key="2" label="工业" :value="2"></el-option>
                                        <el-option :key="3" label="其他" :value="3"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="construction">
                                    <template slot="label">
                                        <span>土地建设情况</span>
                                    </template>
                                    <el-select v-model="formData.construction" placeholder="请选择土地建设情况">
                                        <el-option :key="0" label="空地" :value="0"></el-option>
                                        <el-option :key="1" label="部分在建" :value="1"></el-option>
                                        <el-option :key="2" label="分期开发" :value="2"></el-option>
                                        <el-option :key="3" label="已完成建设" :value="3"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="gainDate">
                                    <template slot="label">
                                        <span>土地获得时间</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.gainDate"
                                        type="date"
                                        placeholder="请选择土地获得时间"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="具体的土地用途" prop="useTypeInput" :required="formData.useType === 3">
                                    <el-input
                                        v-model="formData.useTypeInput"
                                        placeholder="请填写具体的土地用途"
                                        :disabled="formData.useType !== 3">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="gainType">
                                    <template slot="label">
                                        <span>土地性质</span>
                                    </template>
                                    <el-select v-model="formData.gainType" placeholder="请选择土地性质">
                                        <el-option :key="0" label="划拨" :value="0"></el-option>
                                        <el-option :key="1" label="招拍挂出让" :value="1"></el-option>
                                        <el-option :key="2" label="自建" :value="2"></el-option>
                                        <el-option :key="3" label="购买" :value="3"></el-option>
                                        <el-option :key="4" label="赠送" :value="4"></el-option>
                                        <el-option :key="5" label="股权转让" :value="5"></el-option>
                                        <el-option :key="6" label="借用" :value="6"></el-option>
                                        <el-option :key="7" label="租赁" :value="7"></el-option>
                                        <el-option :key="8" label="其他" :value="8"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="landPrice">
                                    <template slot="label">
                                        <span>土地取得价格(万元)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.landPrice" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入土地取得价格"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="landArrears">
                                    <template slot="label">
                                        <span>地价款（租金）欠缴金(万元)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.landArrears" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入地价款（租金）欠缴金"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="landArea">
                                    <template slot="label">
                                        <span>土地总面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('总面积符合以下公式：\n（1）总面积=产权面积+非产权面积\n（2）总面积=可租面积+自用面积+占用面积+借用面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.landArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入土地总面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="rentableArea">
                                    <template slot="label">
                                        <span>可租面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('可租面积符合以下公式：\n（1）可租面积=空置、闲置面积+出租面积\n（2）没有产权但是有对外出租的，也属于可租面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.rentableArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入可租面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="propertyArea">
                                    <template slot="label">
                                        <span>产权面积(㎡)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.propertyArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入产权面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="notPropertyArea">
                                    <template slot="label">
                                        <span>非产权面积(㎡)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.notPropertyArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入非产权面积"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="assetsAmount">
                                    <template slot="label">
                                        <span>资产原值(万元)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.assetsAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入资产原值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="bookAmount">
                                    <template slot="label">
                                        <span>账面价值(万元)</span>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.bookAmount" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入账面价值"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="dateOfBookValue">
                                    <template slot="label">
                                        <span>账面价值时点</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.dateOfBookValue"
                                        type="date"
                                        placeholder="请选择账面价值时点"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="propertyType">
                                    <template slot="label">
                                        <span>是否有产权证</span>
                                    </template>
                                    <el-select v-model="formData.propertyType" placeholder="请选择是否有产权证" @change="handlePropertyTypeChange">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                        <el-option :key="2" label="代管" :value="2"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="warrantDate" :rules="[
                                    { required: formData.propertyType === 1, message: '请选择产权证获得日期', trigger: 'change' }
                                ]">
                                    <template slot="label">
                                        <span>产权证获得日期</span>
                                    </template>
                                    <el-date-picker
                                        v-model="formData.warrantDate"
                                        type="date"
                                        placeholder="请选择产权证获得日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="dateOptions"
                                        :disabled="formData.propertyType === 0"
                                        style="width: 100%;">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="custodyEntrustingParty" :rules="[
                                    { required: formData.propertyType === 2, message: '请输入代管委托方', trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>代管委托方</span>
                                    </template>
                                    <el-input 
                                        v-model="formData.custodyEntrustingParty" 
                                        placeholder="请输入代管委托方"
                                        :disabled="formData.propertyType !== 2">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="warrantIntegration">
                                    <template slot="label">
                                        <span>是否房地权证合一</span>
                                    </template>
                                    <el-select v-model="formData.warrantIntegration" placeholder="请选择是否房地权证合一">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="landWarrant">
                                    <template slot="label">
                                        <span>土地权证(含英文)</span>
                                    </template>
                                    <el-input v-model="formData.landWarrant" placeholder="请输入土地权证(含英文)"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="offAccount">
                                    <template slot="label">
                                        <span>是否账外</span>
                                    </template>
                                    <el-select v-model="formData.offAccount" placeholder="请选择是否账外">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="insuranceOrNot">
                                    <template slot="label">
                                        <span>是否投保</span>
                                    </template>
                                    <el-select v-model="formData.insuranceOrNot" placeholder="请选择是否投保">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="mortgageOrNot">
                                    <template slot="label">
                                        <span>是否抵押或质押</span>
                                    </template>
                                    <el-select v-model="formData.mortgageOrNot" placeholder="请选择是否抵押或质押">
                                        <el-option :key="0" label="否" :value="0"></el-option>
                                        <el-option :key="1" label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="是否竣工财务结算办理" prop="completionOrNot">
                                    <el-select v-model="formData.completionOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否拖欠工程款" prop="owingOrNot">
                                    <el-select v-model="formData.owingOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否具有盘活价值" prop="vitalizeOrNot">
                                    <el-select v-model="formData.vitalizeOrNot" placeholder="请选择">
                                        <el-option label="否" :value="0"></el-option>
                                        <el-option label="是" :value="1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                             <el-col :span="24">
                                <el-form-item label="工作进展" prop="workProgress">
                                    <el-input type="textarea" v-model="formData.workProgress" placeholder="请输入工作进展" :rows="4" maxlength="300" show-word-limit></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                             <el-col :span="24">
                                <el-form-item label="存在问题" prop="problems">
                                    <el-input type="textarea" v-model="formData.problems" placeholder="请输入存在问题" :rows="4" maxlength="300" show-word-limit></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                
                <!-- 资产使用情况 -->
                <div class="form-card">
                    <div class="form-card-header">
                        <h2 class="form-card-title">
                            <i class="fas fa-chart-pie"></i> 资产使用情况
                        </h2>
                    </div>
                    <div class="form-card-body">
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item prop="assetsStatus">
                                    <template slot="label">
                                        <span>资产使用状态</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-select
                                        v-model="formData.assetsStatus"
                                        multiple
                                        placeholder="请选择资产使用状态"
                                        @change="handleAssetsStatusChange">
                                        <el-option :key="0" label="闲置" :value="0"></el-option>
                                        <el-option :key="1" label="自用" :value="1"></el-option>
                                        <el-option :key="2" label="出租" :value="2"></el-option>
                                        <el-option :key="3" label="出借" :value="3"></el-option>
                                        <el-option :key="4" label="占用" :value="4"></el-option>
                                        <el-option :key="5" label="欠租" :value="5"></el-option>
                                        <el-option :key="6" label="转让" :value="6"></el-option>
                                        <el-option :key="7" label="其他" :value="7"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="idleArea" :required="formData.assetsStatus.includes(0)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(0) && (value === null || value === '' || value === 0)) { callback(new Error('请输入空置闲置面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>空置闲置面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.idleArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入空置闲置面积"
                                        :disabled="!formData.assetsStatus.includes(0)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="useArea" :required="formData.assetsStatus.includes(1)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(1) && (value === null || value === '' || value === 0)) { callback(new Error('请输入自用面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>自用面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.useArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入自用面积"
                                        :disabled="!formData.assetsStatus.includes(1)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="rentArea" :required="formData.assetsStatus.includes(2)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(2) && (value === null || value === '' || value === 0)) { callback(new Error('请输入出租面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>出租面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.rentArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入出租面积"
                                        :disabled="!formData.assetsStatus.includes(2)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="lendArea" :required="formData.assetsStatus.includes(3)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(3) && (value === null || value === '' || value === 0)) { callback(new Error('请输入出借面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>出借面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.lendArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入出借面积"
                                        :disabled="!formData.assetsStatus.includes(3)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="occupyArea" :required="formData.assetsStatus.includes(4)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(4) && (value === null || value === '' || value === 0)) { callback(new Error('请输入占用面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>占用面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.occupyArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入占用面积"
                                        :disabled="!formData.assetsStatus.includes(4)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="sellArea" :required="formData.assetsStatus.includes(6)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(6) && (value === null || value === '' || value === 0)) { callback(new Error('请输入转让面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>转让面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.sellArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入转让面积"
                                        :disabled="!formData.assetsStatus.includes(6)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item prop="otherArea" :required="formData.assetsStatus.includes(7)" :rules="[
                                    { validator: (rule, value, callback) => { if (formData.assetsStatus.includes(7) && (value === null || value === '' || value === 0)) { callback(new Error('请输入其他面积')); } else { callback(); } }, trigger: 'blur' }
                                ]">
                                    <template slot="label">
                                        <span>其他面积(㎡)</span>
                                        <i class="fas fa-question-circle tooltip-icon" @click="showTip('请注意各状态面积应符合以下公式，公式不包含\'其他\'状态，后续将取消该状态，原则上该状态面积为0：\n（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积\n（2）出租面积=专业化招租面积+非专业化招商面积\n（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积\n（4）出租面积=可租面积-空置、闲置面积')"></i>
                                    </template>
                                    <el-input-number 
                                        v-model="formData.otherArea" 
                                        :precision="2" 
                                        :min="0" 
                                        :controls="false" 
                                        placeholder="请输入其他面积"
                                        :disabled="!formData.assetsStatus.includes(7)"
                                        @change="validateAreaTotal"
                                        style="width: 100%">
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="备注"
                                    prop="remark"
                                    :required="formData.assetsStatus.includes(7)"
                                    :rules="[{ validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(7) && (!value || value.trim() === '')) { callback(new Error('资产使用状态选择了其他时，请输入备注')); } else { callback(); } }, trigger: 'blur' }]">
                                    <el-input
                                        type="textarea"
                                        v-model="formData.remark"
                                        placeholder="请输入备注"
                                        :rows="4">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                
                <!-- 资产闲置信息（仅当勾选"闲置"时显示） -->
                <template v-if="!isEditMode && formData.assetsStatus.includes(0)">
                    <div class="form-card">
                        <div class="form-card-header">
                            <h2 class="form-card-title" id="idle-info-title">
                                <i class="fas fa-bed"></i> 资产闲置信息
                            </h2>
                            <el-switch v-model="showIdleInfo" active-text="添加闲置信息" inactive-text="不添加"></el-switch>
                        </div>
                        <div class="form-card-body" v-if="showIdleInfo">
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item label="闲置起始日期" :rules="[{ required: true, message: '请选择闲置起始日期', trigger: 'change' }]">
                                        <el-date-picker
                                            v-model="idleInfo.startDate"
                                            type="date"
                                            placeholder="请选择闲置起始日期"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            :picker-options="{ disabledDate: time => time.getTime() > Date.now() }"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="闲置结束日期" :rules="[{ validator: validateIdleEndDate, trigger: 'change' }]">
                                        <el-date-picker
                                            v-model="idleInfo.endDate"
                                            type="date"
                                            placeholder="请选择闲置结束日期"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            :picker-options="{ disabledDate: time => idleInfo.startDate ? time.getTime() <= new Date(idleInfo.startDate).getTime() : false }"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="闲置天数" id="idle-days-title">
                                        <span>{{ idleDays }} 天</span>
                                        <div class="help-text">空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item label="空置闲置面积(㎡)">
                                        <template slot="label">
                                            <span class="required">空置闲置面积(㎡)</span>
                                        </template>
                                        <el-input v-model="formData.idleArea" disabled></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="闲置资产原值(万元)">
                                        <el-input-number 
                                            v-model="idleInfo.originalValue" 
                                            :precision="2" 
                                            :min="0" 
                                            :controls="false" 
                                            placeholder="请输入闲置资产原值"
                                            style="width: 100%">
                                        </el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="闲置资产账面价值(万元)" prop="idleInfo.bookValue" :rules="[{ required: true, message: '请输入闲置资产账面价值', trigger: 'blur' }]">
                                         <el-input-number 
                                            v-model="idleInfo.bookValue" 
                                            :precision="2" 
                                            :min="0" 
                                            :controls="false" 
                                            placeholder="请输入闲置资产账面价值"
                                            style="width: 100%">
                                        </el-input-number>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item label="账面价值时点" prop="idleInfo.bookValueDate" :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]">
                                        <el-date-picker
                                            v-model="idleInfo.bookValueDate"
                                            type="date"
                                            placeholder="请选择账面价值时点"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            :picker-options="dateOptions"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="16">
                                    <el-form-item label="闲置原因" prop="idleInfo.reason" :rules="[{ required: true, message: '请输入闲置原因', trigger: 'blur' }]">
                                        <el-input type="textarea" v-model="idleInfo.reason" :rows="2" placeholder="请输入闲置原因"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="备注">
                                        <el-input type="textarea" v-model="idleInfo.remark" :rows="2" placeholder="请输入备注"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <!-- 盘活记录 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; margin-bottom: 10px;">
                                <h3 class="form-section-title" style="border-bottom: none; margin: 0; padding-bottom: 0;">盘活记录</h3>
                                <el-button type="primary" size="small" icon="el-icon-plus" @click="addRecord">新增盘活记录</el-button>
                            </div>
                            <el-table
                                :data="idleInfo.dealList"
                                border
                                style="width: 100%">
                                <el-table-column min-width="180">
                                    <template slot="header">
                                        <span class="required">日期</span>
                                    </template>
                                    <template slot-scope="scope">
                                        <el-form-item
                                            :prop="'idleInfo.dealList.' + scope.$index + '.date'"
                                            :rules="{ required: true, message: '请选择日期', trigger: 'change' }"
                                            class="record-table-form-item">
                                            <el-date-picker
                                                v-model="scope.row.date"
                                                type="date"
                                                placeholder="请选择日期"
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column min-width="150">
                                    <template slot="header">
                                        <span class="required">是否已盘活</span>
                                    </template>
                                    <template slot-scope="scope">
                                        <el-form-item
                                            :prop="'idleInfo.dealList.' + scope.$index + '.isResult'"
                                            :rules="{ required: true, message: '请选择是否已盘活', trigger: 'change' }"
                                            class="record-table-form-item">
                                            <el-select v-model="scope.row.isResult" placeholder="请选择">
                                                <el-option label="否" :value="0"></el-option>
                                                <el-option label="是" :value="1"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column min-width="180">
                                    <template slot="header">
                                        <span class="required">盘活方式</span>
                                    </template>
                                    <template slot-scope="scope">
                                        <el-form-item
                                            :prop="'idleInfo.dealList.' + scope.$index + '.vitalizeType'"
                                            :rules="{ required: true, message: '请选择盘活方式', trigger: 'change' }"
                                            class="record-table-form-item">
                                            <el-select v-model="scope.row.vitalizeType" placeholder="请选择">
                                                <el-option label="出租" :value="0"></el-option>
                                                <el-option label="出售" :value="1"></el-option>
                                                <el-option label="资产证券化" :value="2"></el-option>
                                                <el-option label="收储" :value="3"></el-option>
                                                <el-option label="转为自用" :value="4"></el-option>
                                                <el-option label="转为借用" :value="5"></el-option>
                                                <el-option label="转为占用" :value="6"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="reason" label="已采取的盘活管理措施" min-width="300">
                                    <template slot-scope="scope">
                                        <el-form-item 
                                            :prop="'idleInfo.dealList.' + scope.$index + '.reason'"
                                            class="record-table-form-item">
                                            <el-input 
                                                type="textarea" 
                                                v-model="scope.row.reason" 
                                                placeholder="请输入已采取的盘活管理措施" 
                                                :rows="2">
                                            </el-input>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="nextReason" label="下一步建议" min-width="300">
                                    <template slot-scope="scope">
                                        <el-form-item 
                                            :prop="'idleInfo.dealList.' + scope.$index + '.nextReason'"
                                            class="record-table-form-item">
                                            <el-input 
                                                type="textarea" 
                                                v-model="scope.row.nextReason" 
                                                placeholder="请输入下一步建议" 
                                                :rows="2">
                                            </el-input>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="80" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button
                                            type="danger"
                                            size="mini"
                                            icon="el-icon-delete"
                                            circle
                                            @click="removeRecord(scope.$index)">
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div v-if="!idleInfo.dealList || idleInfo.dealList.length === 0" class="empty-text">
                                暂无盘活记录，请点击上方"新增盘活记录"按钮添加。
                            </div>
                        </div>
                    </div>
                </template>
                
                <!-- 表单提交按钮 -->
                <div class="form-footer">
                    <el-button @click="resetForm">重置</el-button>
                    <el-button type="primary" @click="submitForm">提交</el-button>
                </div>
            </el-form>
        </div>
    </div>

    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <!-- 引入ElementUI组件库 -->
    <script src="assets/element-ui/index.js"></script>
    <!-- 引入axios -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js"></script>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    isEditMode: false, // 是否为编辑模式
                    currentUser: '张三', // 模拟当前登录用户
                    enterpriseOptions: [ // 从 enterpriseList.html 获取的企业信息
                        { value: 0, label: '厦门市城市建设发展投资有限公司' },
                        { value: 1, label: '厦门市地热资源管理有限公司' },
                        { value: 2, label: '厦门兴地房屋征迁服务有限公司' },
                        { value: 3, label: '厦门地丰置业有限公司' },
                        { value: 4, label: '图智策划咨询（厦门）有限公司' },
                        { value: 5, label: '厦门市集众祥和物业管理有限公司' },
                        { value: 6, label: '厦门市人居乐业物业服务有限公司' }
                    ],
                    // 表单数据
                    formData: {
                        // 基本信息
                        code: '', // 资产编号
                        enterpriseCode: '', // 企业自定义编号
                        name: '', // 资产项目（资产名称）
                        groupName: 0, // 所属集团，默认为"厦门市城市建设发展投资有限公司"
                        companyName: '', // 所属企业
                        ownUnit: '', // 权属单位名称
                        manageUnit: '', // 管理单位
                        reportOrNot: '', // 是否报送国资委
                        operator: '', // 经办人
                        entryClerk: '', // 录入人
                        createTime: '', // 录入时间
                        region: [], // 省市区
                        province: '', // 省
                        city: '', // 市
                        area: '', // 区
                        address: '', // 详细地址
                        status: 0, // 状态，默认为"草稿"
                        // 土地信息
                        useType: '', // 土地用途
                        useTypeInput: '', // 具体的土地用途
                        construction: '', // 土地建设情况
                        gainDate: '', // 土地获得时间
                        gainType: '', // 取得来源
                        landPrice: '', // 土地取得价格
                        landArrears: '', // 地价款欠缴金
                        landArea: '', // 土地总面积
                        rentableArea: '', // 可租面积
                        propertyArea: '', // 产权面积
                        notPropertyArea: '', // 非产权面积
                        assetsAmount: '', // 资产原值
                        bookAmount: '', // 账面价值
                        dateOfBookValue: '', // 账面价值时点
                        propertyType: '', // 是否有产权证
                        warrantDate: '', // 产权证获得日期
                        custodyEntrustingParty: '', // 代管委托方
                        warrantIntegration: '', // 是否房地权证合一
                        landWarrant: '', // 土地权证
                        offAccount: '', // 是否账外
                        insuranceOrNot: 0, // 是否投保，默认为"否"
                        mortgageOrNot: '', // 是否抵押，默认为"否"
                        completionOrNot: '', // 是否竣工财务结算办理
                        owingOrNot: '', // 是否拖欠工程款
                        vitalizeOrNot: '', // 是否具有盘活价值
                        workProgress: '', // 工作进展
                        problems: '', // 存在问题
                        remark: '', // 备注
                        // 资产使用情况
                        assetsStatus: [], // 资产使用状态
                        idleArea: 0, // 闲置面积
                        useArea: 0, // 自用面积
                        rentArea: 0, // 出租面积
                        lendArea: 0, // 出借面积
                        occupyArea: 0, // 占用面积
                        sellArea: 0, // 转让面积
                        otherArea: 0 // 其他面积
                    },
                    statusOptions: [], // 状态下拉框选项
                    // 资产闲置信息相关
                    showIdleInfo: true, // 添加闲置信息开关，默认打开
                    idleInfo: {
                        startDate: '', // 闲置起始日期
                        endDate: '',   // 闲置结束日期
                        originalValue: '', // 闲置资产原值
                        bookValue: '',     // 闲置资产账面价值
                        bookValueDate: '', // 账面价值时点
                        reason: '',        // 闲置原因
                        remark: '',         // 备注
                        dealList: []
                    },
                    // 省市区数据
                    regionOptions: [
                        {
                            value: '福建省',
                            label: '福建省',
                            children: [
                                {
                                    value: '厦门市',
                                    label: '厦门市',
                                    children: [
                                        { value: '思明区', label: '思明区' },
                                        { value: '湖里区', label: '湖里区' },
                                        { value: '集美区', label: '集美区' },
                                        { value: '海沧区', label: '海沧区' },
                                        { value: '同安区', label: '同安区' },
                                        { value: '翔安区', label: '翔安区' }
                                    ]
                                },
                                {
                                    value: '福州市',
                                    label: '福州市',
                                    children: [
                                        { value: '鼓楼区', label: '鼓楼区' },
                                        { value: '台江区', label: '台江区' },
                                        { value: '仓山区', label: '仓山区' },
                                        { value: '马尾区', label: '马尾区' },
                                        { value: '晋安区', label: '晋安区' }
                                    ]
                                }
                            ]
                        },
                        {
                            value: '浙江省',
                            label: '浙江省',
                            children: [
                                {
                                    value: '杭州市',
                                    label: '杭州市',
                                    children: [
                                        { value: '上城区', label: '上城区' },
                                        { value: '下城区', label: '下城区' },
                                        { value: '江干区', label: '江干区' },
                                        { value: '拱墅区', label: '拱墅区' },
                                        { value: '西湖区', label: '西湖区' }
                                    ]
                                }
                            ]
                        }
                    ],
                    
                    // 日期选择器配置
                    dateOptions: {
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    
                    // 表单验证规则
                    rules: {
                        // 基本信息验证规则
                        code: [
                            { required: true, message: '资产编号为必填项' }
                        ],
                        enterpriseCode: [
                            { required: true, message: '请输入企业自定义编号', trigger: 'blur' }
                        ],
                        name: [
                            { required: true, message: '请输入资产项目（资产名称）', trigger: 'blur' }
                        ],
                        companyName: [
                            { required: true, message: '请选择所属企业', trigger: 'change' }
                        ],
                        ownUnit: [
                            { required: true, message: '请输入权属单位名称', trigger: 'blur' }
                        ],
                        manageUnit: [
                            { required: true, message: '请选择管理单位', trigger: 'change' }
                        ],
                        reportOrNot: [
                            { required: true, message: '请选择是否报送国资委', trigger: 'change' }
                        ],
                        operator: [
                            { required: true, message: '请输入经办人', trigger: 'blur' }
                        ],
                        region: [
                            { required: true, message: '请选择资产位置', trigger: 'change' }
                        ],
                        status: [
                            { required: true, message: '请选择状态', trigger: 'change' }
                        ],
                        
                        // 土地信息验证规则
                        useType: [
                            { required: true, message: '请选择土地用途（运营方式和情况）', trigger: 'change' }
                        ],
                        useTypeInput: [
                            { validator: (rule, value, callback) => {
                                if (this.formData.useType === 3 && (!value || value.trim() === '')) {
                                    callback(new Error('请填写具体的土地用途'));
                                } else {
                                    callback();
                                }
                              }, trigger: 'blur' 
                            }
                        ],
                        construction: [
                            { required: true, message: '请选择土地建设情况', trigger: 'change' }
                        ],
                        gainDate: [
                            { required: true, message: '请选择土地获得时间', trigger: 'change' }
                        ],
                        gainType: [
                            { required: true, message: '请选择土地性质', trigger: 'change' }
                        ],
                        landArea: [
                            { required: true, message: '请输入土地总面积', trigger: 'blur' }
                        ],
                        rentableArea: [
                            { required: true, message: '请输入可租面积', trigger: 'blur' }
                        ],
                        propertyArea: [
                            { required: true, message: '请输入产权面积', trigger: 'blur' }
                        ],
                        notPropertyArea: [
                            { required: true, message: '请输入非产权面积', trigger: 'blur' }
                        ],
                        assetsAmount: [
                            { required: true, message: '请输入资产原值', trigger: 'blur' }
                        ],
                        bookAmount: [
                            { required: true, message: '请输入账面价值', trigger: 'blur' }
                        ],
                        dateOfBookValue: [
                            { required: true, message: '请选择账面价值时点', trigger: 'change' }
                        ],
                        propertyType: [
                            { required: true, message: '请选择是否有产权证', trigger: 'change' }
                        ],
                        warrantIntegration: [
                            { required: true, message: '请选择是否房地权证合一', trigger: 'change' }
                        ],
                        offAccount: [
                            { required: true, message: '请选择是否账外', trigger: 'change' }
                        ],
                        mortgageOrNot: [
                            { required: true, message: '请选择是否抵押或质押', trigger: 'change' }
                        ],
                        vitalizeOrNot: [
                            { required: true, message: '请选择是否具有盘活价值', trigger: 'change' }
                        ],
                        workProgress: [
                            { required: true, message: '请输入工作进展', trigger: 'blur' },
                            { max: 300, message: '工作进展不能超过300个字', trigger: 'blur' }
                        ],
                        problems: [
                            { required: true, message: '请输入存在问题', trigger: 'blur' },
                            { max: 300, message: '存在问题不能超过300个字', trigger: 'blur' }
                        ],
                        
                        // 资产使用情况验证规则
                        assetsStatus: [
                            { required: true, message: '请选择资产使用状态', trigger: 'change' }
                        ],
                        idleArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(0) && (value === null || value === '')) { callback(new Error('请输入空置闲置面积')); } else { callback(); } }, trigger: 'blur' }
                        ],
                        useArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(1) && (value === null || value === '')) { callback(new Error('请输入自用面积')); } else { callback(); } }, trigger: 'blur' }
                        ],
                        rentArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(2) && (value === null || value === '')) { callback(new Error('请输入出租面积')); } else { callback(); } }, trigger: 'blur' }
                        ],
                        lendArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(3) && (value === null || value === '')) { callback(new Error('请输入出借面积')); } else { callback(); } }, trigger: 'blur' }
                        ],
                        occupyArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(4) && (value === null || value === '')) { callback(new Error('请输入占用面积')); } else { callback(); } }, trigger: 'blur' }
                        ],
                        sellArea: [
                            { validator: (rule, value, callback) => { if (this.formData.assetsStatus.includes(6) && (value === null || value === '')) { callback(new Error('请输入转让面积')); } else { callback(); } }, trigger: 'blur' }
                        ]
                    }
                };
            },
            computed: {
                idleDays() {
                    if (this.idleInfo.startDate && this.idleInfo.endDate) {
                        const start = new Date(this.idleInfo.startDate);
                        const end = new Date(this.idleInfo.endDate);
                        const days = Math.floor((end - start) / (1000 * 60 * 60 * 24));
                        return days > 0 ? days : 0;
                    }
                    return 0;
                }
            },
            methods: {
                // 区域选择变更处理
                handleRegionChange(value) {
                    if (value && value.length === 3) {
                        this.formData.province = value[0];
                        this.formData.city = value[1];
                        this.formData.area = value[2];
                    }
                },
                
                // 所属企业变更处理
                handleCompanyChange(value) {
                    const selectedCompany = this.enterpriseOptions.find(item => item.value === value);
                    if (selectedCompany) {
                        this.formData.ownUnit = selectedCompany.label;
                    }
                },
                
                // 土地用途变更处理
                handleUseTypeChange() {
                    if (this.formData.useType !== 3) {
                        this.formData.useTypeInput = '';
                    }
                    this.$nextTick(() => {
                        this.$refs.landForm.validateField('useTypeInput');
                    });
                },
                
                // 产权证变更处理
                handlePropertyTypeChange() {
                    // 根据是否有产权证，控制相关字段
                    if (this.formData.propertyType === 0) {
                        // 如果选择"否"，则清空产权证获得日期和代管委托方
                        this.formData.warrantDate = '';
                        this.formData.custodyEntrustingParty = '';
                    } else if (this.formData.propertyType === 1) {
                        // 如果选择"是"，则清空代管委托方
                        this.formData.custodyEntrustingParty = '';
                    } else if (this.formData.propertyType === 2) {
                        // 如果选择"代管"，则清空产权证获得日期
                        this.formData.warrantDate = '';
                    }
                    
                    this.$nextTick(() => {
                        this.$refs.landForm.validateField('warrantDate');
                        this.$refs.landForm.validateField('custodyEntrustingParty');
                    });
                },
                
                // 资产使用状态变更处理
                handleAssetsStatusChange(values) {
                    if (!values.includes(0)) this.formData.idleArea = 0;
                    if (!values.includes(1)) this.formData.useArea = 0;
                    if (!values.includes(2)) this.formData.rentArea = 0;
                    if (!values.includes(3)) this.formData.lendArea = 0;
                    if (!values.includes(4)) this.formData.occupyArea = 0;
                    if (!values.includes(6)) this.formData.sellArea = 0;
                    if (!values.includes(7)) this.formData.otherArea = 0;

                    // 新增：勾选闲置时初始化闲置信息
                    if (values.includes(0)) {
                        this.showIdleInfo = true;
                        // 自动带出数据，但允许用户修改
                        this.idleInfo.originalValue = this.formData.assetsAmount;
                        this.idleInfo.bookValue = this.formData.bookAmount;
                        this.idleInfo.bookValueDate = this.formData.dateOfBookValue;
                        if (!this.idleInfo.startDate && this.formData.gainDate) {
                            this.idleInfo.startDate = this.formData.gainDate;
                        }
                    } else {
                        this.showIdleInfo = false;
                    }
                },
                
                // 验证面积总和
                validateAreaTotal() {
                    // 验证面积公式
                    const total = parseFloat(this.formData.landArea || 0);
                    const sum = parseFloat(this.formData.idleArea || 0) + 
                                parseFloat(this.formData.useArea || 0) + 
                                parseFloat(this.formData.rentArea || 0) + 
                                parseFloat(this.formData.lendArea || 0) + 
                                parseFloat(this.formData.occupyArea || 0);
                    
                    if (total > 0 && Math.abs(total - sum) > 0.01) {
                        this.$message({
                            type: 'warning',
                            message: '请注意：总面积应等于闲置面积+自用面积+出租面积+出借面积+占用面积'
                        });
                    }
                    
                    // 验证产权和非产权面积
                    const propertySum = parseFloat(this.formData.propertyArea || 0) + 
                                        parseFloat(this.formData.notPropertyArea || 0);
                    if (total > 0 && Math.abs(total - propertySum) > 0.01) {
                        this.$message({
                            type: 'warning',
                            message: '请注意：总面积应等于产权面积+非产权面积'
                        });
                    }
                },
                
                // 显示提示信息
                showTip(message) {
                    this.$message({
                        message: message,
                        type: 'info',
                        showClose: true,
                        duration: 10000 // 提示时间更长，方便用户阅读
                    });
                },
                
                // 重置表单
                resetForm() {
                    this.$confirm('确定要重置表单吗？所有已填写的数据将会丢失。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$refs.landForm.resetFields();
                        // 恢复默认值
                        this.formData.groupName = 0;
                        this.formData.status = 0;
                        this.formData.offAccount = '';
                        this.formData.insuranceOrNot = 0;
                        this.formData.mortgageOrNot = '';
                        
                        this.$message({
                            type: 'success',
                            message: '表单已重置！'
                        });
                    }).catch(() => {
                        // 取消重置操作
                    });
                },
                
                // 提交表单
                submitForm() {
                    this.$refs.landForm.validate((valid) => {
                        if (valid) {
                            // 表单验证通过，额外验证面积
                            this.validateAreaTotal();
                            
                            // 验证是否有选择省市区
                            if (!this.formData.region || this.formData.region.length < 3) {
                                this.$message.error('请选择完整的省/市/区');
                                return;
                            }
                            
                            // 验证产权证相关条件
                            if (this.formData.propertyType === 1 && !this.formData.warrantDate) {
                                this.$message.error('选择"有产权证"时，产权证获得日期必填');
                                return;
                            }
                            
                            if (this.formData.propertyType === 2 && !this.formData.custodyEntrustingParty) {
                                this.$message.error('选择"代管"时，代管委托方必填');
                                return;
                            }
                            
                            // 验证资产使用状态和对应面积
                            for (const status of this.formData.assetsStatus) {
                                switch (status) {
                                    case 0: // 闲置
                                        if (this.formData.idleArea <= 0) {
                                            this.$message.error('选择了"闲置"状态，空置闲置面积必须大于0');
                                            return;
                                        }
                                        break;
                                    case 1: // 自用
                                        if (this.formData.useArea <= 0) {
                                            this.$message.error('选择了"自用"状态，自用面积必须大于0');
                                            return;
                                        }
                                        break;
                                    case 2: // 出租
                                        if (this.formData.rentArea <= 0) {
                                            this.$message.error('选择了"出租"状态，出租面积必须大于0');
                                            return;
                                        }
                                        break;
                                    case 3: // 出借
                                        if (this.formData.lendArea <= 0) {
                                            this.$message.error('选择了"出借"状态，出借面积必须大于0');
                                            return;
                                        }
                                        break;
                                    case 4: // 占用
                                        if (this.formData.occupyArea <= 0) {
                                            this.$message.error('选择了"占用"状态，占用面积必须大于0');
                                            return;
                                        }
                                        break;
                                    case 6: // 转让
                                        if (this.formData.sellArea <= 0) {
                                            this.$message.error('选择了"转让"状态，转让面积必须大于0');
                                            return;
                                        }
                                        break;
                                }
                            }
                            
                            // 新增：如需填写闲置信息则校验其必填项
                            if (this.formData.assetsStatus.includes(0) && this.showIdleInfo) {
                                // 触发表单对闲置信息部分的校验
                                const idleFormItems = ['idleInfo.bookValue', 'idleInfo.bookValueDate', 'idleInfo.reason'];
                                let idleValid = true;
                                this.$refs.landForm.validateField(idleFormItems, (errorMessage) => {
                                    if(errorMessage) {
                                        idleValid = false;
                                    }
                                });

                                if(!idleValid) {
                                    this.$message.error('请填写完整的资产闲置信息');
                                    return;
                                }

                                // 校验闲置信息必填项
                                if (!this.idleInfo.startDate) {
                                    this.$message.error('请填写闲置起始日期');
                                    return;
                                }
                                if (this.idleInfo.endDate && new Date(this.idleInfo.endDate) <= new Date(this.idleInfo.startDate)) {
                                    this.$message.error('闲置结束日期应大于起始日期');
                                    return;
                                }
                            }
                            
                            // 准备提交数据
                            this.$message({
                                type: 'info',
                                message: '正在提交数据...'
                            });
                            
                            // 根据状态处理不同的提交逻辑
                            const statusMap = {
                                0: '草稿',
                                1: '备案',
                                2: '撤回',
                                4: '作废'
                            };
                            
                            // 模拟API调用
                            setTimeout(() => {
                                console.log('提交的表单数据：', this.formData);
                                
                                // 模拟成功提交并生成编码
                                const randomCode = 'L' + new Date().getFullYear() + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                                this.formData.code = randomCode;
                                
                                // 模拟成功提交
                                this.$message({
                                    type: 'success',
                                    message: `${statusMap[this.formData.status]}保存成功！资产编号：${this.formData.code}`
                                });
                                
                                // 如果是备案，则禁用表单编辑
                                if (this.formData.status === 1) {
                                    // 实际项目中可能还需要重新加载表单或跳转页面
                                }
                            }, 1000);
                        } else {
                            this.$message({
                                type: 'error',
                                message: '表单验证失败，请检查并完善表单信息！'
                            });
                            return false;
                        }
                    });
                },
                // 闲置结束日期校验
                validateIdleEndDate(rule, value, callback) {
                    if (value && this.idleInfo.startDate) {
                        if (new Date(value) <= new Date(this.idleInfo.startDate)) {
                            callback(new Error('闲置结束日期应大于起始日期'));
                            return;
                        }
                    }
                    callback();
                },
                // 格式化日期
                formatDate(date) {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },
                // 设置用户和时间默认值
                setDefaultUserInfo() {
                    this.formData.operator = this.currentUser;
                    this.formData.entryClerk = this.currentUser;
                    this.formData.createTime = this.formatDate(new Date());
                },
                
                addRecord() {
                    this.idleInfo.dealList.push({
                        date: '',
                        isResult: '',
                        vitalizeType: '',
                        reason: '',
                        nextReason: ''
                    });
                },
                removeRecord(index) {
                    this.$confirm('确定要删除这条盘活记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.idleInfo.dealList.splice(index, 1);
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                    }).catch(() => {});
                },
                // 加载编辑数据
                loadEditData(id) {
                    this.$message.info('正在加载资产信息...');
                    // 模拟API调用
                    setTimeout(() => {
                        // 这是一份模拟数据，用于填充编辑表单
                        this.formData = {
                            code: 'TD001620240700001',
                            enterpriseCode: 'E12345',
                            name: '被编辑的测试土地资产',
                            groupName: 0,
                            companyName: 1,
                            ownUnit: '厦门市地热资源管理有限公司',
                            manageUnit: 2,
                            reportOrNot: 1,
                            operator: '李四',
                            entryClerk: '王五',
                            createTime: '2023-10-15 10:30:00',
                            region: ['福建省', '厦门市', '湖里区'],
                            province: '福建省',
                            city: '厦门市',
                            area: '湖里区',
                            address: '厦门市湖里区金山街道',
                            status: 1, // 备案
                            useType: 1, // 商业
                            useTypeInput: '',
                            construction: 3, // 已完成建设
                            gainDate: '2022-01-20',
                            gainType: 1,
                            landPrice: 5000.00,
                            landArrears: 0.00,
                            landArea: 10000.00,
                            rentableArea: 8000.00,
                            propertyArea: 10000.00,
                            notPropertyArea: 0.00,
                            assetsAmount: 12000.00,
                            bookValue: 9500.00,
                            dateOfBookValue: '2024-06-30',
                            propertyType: 1,
                            warrantDate: '2022-05-10',
                            custodyEntrustingParty: '',
                            warrantIntegration: 1,
                            landWarrant: '闽(2022)厦门市不动产权第0012345号',
                            offAccount: 0,
                            insuranceOrNot: 1,
                            mortgageOrNot: 0,
                            completionOrNot: 1,
                            owingOrNot: 0,
                            vitalizeOrNot: 1,
                            workProgress: '所有权清晰，已投入商业运营。',
                            problems: '暂无重大问题。',
                            remark: '这是一个用于编辑演示的备注。',
                            assetsStatus: [1, 2], // 自用、出租。确保不包含0（闲置）
                            idleArea: 0,
                            useArea: 2000.00,
                            rentArea: 6000.00,
                            lendArea: 0,
                            occupyArea: 0,
                            sellArea: 0,
                            otherArea: 0
                        };
                        this.$message.success('资产信息加载成功！');
                        // 更新状态选项
                        this.updateStatusOptions();
                    }, 500);
                },

                // 更新状态下拉框的选项
                updateStatusOptions() {
                    const currentStatus = this.formData.status;
                    let options = [];

                    if (this.isEditMode) {
                        // 编辑模式下根据状态动态生成选项
                        if (currentStatus === 0) { // 草稿
                            options = [
                                { value: 0, label: '草稿' },
                                { value: 4, label: '作废' }
                            ];
                        } else if (currentStatus === 1) { // 备案
                            options = [
                                { value: 1, label: '备案' },
                                { value: 2, label: '撤回' }
                            ];
                        } else if (currentStatus === 2) { // 撤回
                            options = [
                                { value: 2, label: '撤回' },
                                { value: 4, label: '作废' }
                            ];
                        } else if (currentStatus === 4) { // 作废
                            options = [{ value: 4, label: '作废', disabled: true }];
                        }
                    } else {
                        // 新增模式下仅提供草稿和备案
                        options = [
                            { value: 0, label: '草稿' },
                            { value: 1, label: '备案' }
                        ];
                    }

                    this.statusOptions = options;
                },

                // 状态变更处理
                handleStatusChange(newStatus) {
                    // 当状态变化时，也需要更新选项，以应对可能的回退操作
                    if (this.isEditMode) {
                        this.updateStatusOptions();
                    }
                }
            },
            mounted() {
                const urlParams = new URLSearchParams(window.location.search);
                const id = urlParams.get('id');
                if (id) {
                    this.isEditMode = true;
                    this.loadEditData(id);
                } else {
                    // 新增模式
                    this.formData.groupName = 0;
                    this.setDefaultUserInfo();
                    this.updateStatusOptions(); // 初始化新增模式的状态选项
                }
            }
        });
    </script>

</body>
</html>
