<template>
  <div class="land-form">
    <div class="p-4">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="企业自定义编号" name="enterpriseCode" :rules="[{ required: true, message: '请输入企业自定义编号', trigger: 'blur' }]">
                  <a-input v-model:value="formData.enterpriseCode" placeholder="请输入企业自定义编号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="name" :rules="[{ required: true, message: '请输入资产项目（资产名称）', trigger: 'blur' }]">
                  <template #label>
                    资产项目（资产名称）
                    <a-tooltip title="系统内要求资产名称唯一">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.name" placeholder="请输入资产项目（资产名称）" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="所属集团" name="groupName">
                  <JDictSelectTag v-model:value="formData.groupName" placeholder="请选择所属集团" disabled dictCode="group_name" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业" name="companyName" :rules="[{ required: true, message: '请选择所属企业', trigger: 'change' }]">
                  <a-select v-model:value="formData.companyName" placeholder="请选择所属企业">
                    <a-select-option :value="0">厦门市城市建设发展投资有限公司</a-select-option>
                    <a-select-option :value="1">厦门市地热资源管理有限公司</a-select-option>
                    <a-select-option :value="2">厦门兴地房屋征迁服务有限公司</a-select-option>
                    <a-select-option :value="3">厦门地丰置业有限公司</a-select-option>
                    <a-select-option :value="4">图智策划咨询（厦门）有限公司</a-select-option>
                    <a-select-option :value="5">厦门市集众祥和物业管理有限公司</a-select-option>
                    <a-select-option :value="6">厦门市人居乐业物业服务有限公司</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="权属单位名称" name="ownUnit" :rules="[{ required: true, message: '请输入权属单位名称', trigger: 'blur' }]">
                  <a-input v-model:value="formData.ownUnit" placeholder="请输入权属单位名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产位置" name="region" :rules="[{ required: true, message: '请选择省/市/区', trigger: 'change' }]">
                  <JAreaLinkage v-model:value="formData.region" placeholder="请选择省/市/区" :showArea="true" :showAll="false" saveCode="region" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="详细地址" name="address">
                  <a-input v-model:value="formData.address" placeholder="请输入详细地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status" :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]">
                  <template #label>
                    状态
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option :value="0">草稿</a-select-option>
                    <a-select-option :value="1">备案</a-select-option>
                    <a-select-option :value="2">撤回</a-select-option>
                    <a-select-option :value="4">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit" :rules="[{ required: true, message: '请选择管理单位', trigger: 'change' }]">
                  <a-select v-model:value="formData.manageUnit" placeholder="请选择管理单位">
                    <a-select-option :value="0">厦门市城市建设发展投资有限公司</a-select-option>
                    <a-select-option :value="1">厦门市地热资源管理有限公司</a-select-option>
                    <a-select-option :value="2">厦门兴地房屋征迁服务有限公司</a-select-option>
                    <a-select-option :value="3">厦门地丰置业有限公司</a-select-option>
                    <a-select-option :value="4">图智策划咨询（厦门）有限公司</a-select-option>
                    <a-select-option :value="5">厦门市集众祥和物业管理有限公司</a-select-option>
                    <a-select-option :value="6">厦门市人居乐业物业服务有限公司</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <a-select v-model:value="formData.reportOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator" :rules="[{ required: true, message: '请输入经办人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk" :rules="[{ required: true, message: '请输入录入人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.entryClerk" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime" :rules="[{ required: true, message: '请输入录入时间', trigger: 'blur' }]">
                  <a-input v-model:value="formData.createTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 土地信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:map-outlined" class="title-icon" />
              土地信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="土地用途（运营方式和情况）"
                  :labelCol="{ span: 12 }"
                  name="useType"
                  :rules="[{ required: true, message: '请选择土地用途（运营方式和情况）', trigger: 'change' }]"
                >
                  <a-select v-model:value="formData.useType" placeholder="请选择土地用途（运营方式和情况）">
                    <a-select-option :value="0">居住</a-select-option>
                    <a-select-option :value="1">商业</a-select-option>
                    <a-select-option :value="2">工业</a-select-option>
                    <a-select-option :value="3">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="具体的土地用途" name="useTypeInput">
                  <a-input v-model:value="formData.useTypeInput" placeholder="请填写具体的土地用途" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地建设情况" name="construction" :rules="[{ required: true, message: '请选择土地建设情况', trigger: 'change' }]">
                  <a-select v-model:value="formData.construction" placeholder="请选择土地建设情况">
                    <a-select-option :value="0">空地</a-select-option>
                    <a-select-option :value="1">部分在建</a-select-option>
                    <a-select-option :value="2">分期开发</a-select-option>
                    <a-select-option :value="3">已完成建设</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="土地获得时间" name="gainDate" :rules="[{ required: true, message: '请选择土地获得时间', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="formData.gainDate"
                    placeholder="请选择土地获得时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地性质" name="gainType" :rules="[{ required: true, message: '请选择土地性质', trigger: 'change' }]">
                  <a-select v-model:value="formData.gainType" placeholder="请选择土地性质">
                    <a-select-option :value="0">划拨</a-select-option>
                    <a-select-option :value="1">招拍挂出让</a-select-option>
                    <a-select-option :value="2">自建</a-select-option>
                    <a-select-option :value="3">购买</a-select-option>
                    <a-select-option :value="4">赠送</a-select-option>
                    <a-select-option :value="5">股权转让</a-select-option>
                    <a-select-option :value="6">借用</a-select-option>
                    <a-select-option :value="7">租赁</a-select-option>
                    <a-select-option :value="8">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="土地取得价格(万元)" name="landPrice">
                  <a-input-number
                    v-model:value="formData.landPrice"
                    placeholder="请输入土地取得价格"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item :labelCol="{ span: 12 }" label="地价款（租金）欠缴金(万元)" name="landArrears">
                  <a-input-number
                    v-model:value="formData.landArrears"
                    placeholder="请输入地价款（租金）欠缴金"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="landArea" :rules="[{ required: true, message: '请输入土地总面积', trigger: 'blur' }]">
                  <template #label>
                    土地总面积(㎡)
                    <a-tooltip title="总面积符合以下公式：（1）总面积=产权面积+非产权面积（2）总面积=可租面积+自用面积+占用面积+借用面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.landArea"
                    placeholder="请输入土地总面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="rentableArea" :rules="[{ required: true, message: '请输入可租面积', trigger: 'blur' }]">
                  <template #label>
                    可租面积(㎡)
                    <a-tooltip title="可租面积符合以下公式：（1）可租面积=空置、闲置面积+出租面积（2）没有产权但是有对外出租的，也属于可租面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentableArea"
                    placeholder="请输入可租面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="产权面积(㎡)" name="propertyArea" :rules="[{ required: true, message: '请输入产权面积', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.propertyArea"
                    placeholder="请输入产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="非产权面积(㎡)" name="notPropertyArea" :rules="[{ required: true, message: '请输入非产权面积', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.notPropertyArea"
                    placeholder="请输入非产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产原值(万元)" name="assetsAmount" :rules="[{ required: true, message: '请输入资产原值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    placeholder="请输入资产原值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="账面价值(万元)" name="bookAmount" :rules="[{ required: true, message: '请输入账面价值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    placeholder="请输入账面价值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面价值时点" name="dateOfBookValue" :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否有产权证" name="propertyType" :rules="[{ required: true, message: '请选择是否有产权证', trigger: 'change' }]">
                  <a-select v-model:value="formData.propertyType" placeholder="请选择是否有产权证">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                    <a-select-option :value="2">代管</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="产权证获得日期" name="warrantDate">
                  <a-date-picker
                    v-model:value="formData.warrantDate"
                    placeholder="请选择产权证获得日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="代管委托方" name="custodyEntrustingParty">
                  <a-input v-model:value="formData.custodyEntrustingParty" placeholder="请输入代管委托方" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否房地权证合一" name="warrantIntegration" :rules="[{ required: true, message: '请选择是否房地权证合一', trigger: 'change' }]">
                  <a-select v-model:value="formData.warrantIntegration" placeholder="请选择是否房地权证合一">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="土地权证(含英文)" name="landWarrant">
                  <a-input v-model:value="formData.landWarrant" placeholder="请输入土地权证(含英文)" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否账外" name="offAccount" :rules="[{ required: true, message: '请选择是否账外', trigger: 'change' }]">
                  <a-select v-model:value="formData.offAccount" placeholder="请选择是否账外">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否投保" name="insuranceOrNot" :rules="[{ required: true, message: '请选择是否投保', trigger: 'change' }]">
                  <a-select v-model:value="formData.insuranceOrNot" placeholder="请选择是否投保">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否抵押或质押" name="mortgageOrNot" :rules="[{ required: true, message: '请选择是否抵押或质押', trigger: 'change' }]">
                  <a-select v-model:value="formData.mortgageOrNot" placeholder="请选择是否抵押或质押">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否竣工财务结算办理" name="completionOrNot">
                  <a-select v-model:value="formData.completionOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否拖欠工程款" name="owingOrNot">
                  <a-select v-model:value="formData.owingOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否具有盘活价值" name="vitalizeOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <a-select v-model:value="formData.vitalizeOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="工作进展"
                  :labelCol="{ span: 3 }"
                  name="workProgress"
                  :rules="[{ required: true, message: '请输入工作进展', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.workProgress" placeholder="请输入工作进展" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="存在问题"
                  :labelCol="{ span: 3 }"
                  name="problems"
                  :rules="[{ required: true, message: '请输入存在问题', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.problems" placeholder="请输入存在问题" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产使用情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:pie-chart-outlined" class="title-icon" />
              资产使用情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item name="assetsStatus" :rules="[{ required: true, message: '请选择资产使用状态', trigger: 'change' }]">
                  <template #label>
                    资产使用状态
                    <a-tooltip title="选择'闲置'状态时，将显示闲置信息填写区域">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.assetsStatus" mode="multiple" placeholder="请选择资产使用状态">
                    <a-select-option :value="0">闲置</a-select-option>
                    <a-select-option :value="1">自用</a-select-option>
                    <a-select-option :value="2">出租</a-select-option>
                    <a-select-option :value="3">出借</a-select-option>
                    <a-select-option :value="4">占用</a-select-option>
                    <a-select-option :value="5">欠租</a-select-option>
                    <a-select-option :value="6">转让</a-select-option>
                    <a-select-option :value="7">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="idleArea">
                  <template #label>
                    空置闲置面积(㎡)
                    <a-tooltip title="空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.idleArea"
                    placeholder="请输入空置闲置面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="useArea">
                  <template #label>
                    自用面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.useArea"
                    placeholder="请输入自用面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="rentArea">
                  <template #label>
                    出租面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentArea"
                    placeholder="请输入出租面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="lendArea">
                  <template #label>
                    出借面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.lendArea"
                    placeholder="请输入出借面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupyArea">
                  <template #label>
                    占用面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.occupyArea"
                    placeholder="请输入占用面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="sellArea">
                  <template #label>
                    转让面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.sellArea"
                    placeholder="请输入转让面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="otherArea">
                  <template #label>
                    其他面积(㎡)
                    <a-tooltip title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.otherArea"
                    placeholder="请输入其他面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="备注" name="remark">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产闲置信息（仅当勾选"闲置"时显示） -->
        <div v-if="showIdleInfo" class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:bed-outlined" class="title-icon" />
              资产闲置信息
            </div>
            <div class="form-card-action">
              <a-switch v-model:checked="showIdleInfo" checked-children="添加闲置信息" un-checked-children="不添加" />
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="闲置起始日期" :rules="[{ required: true, message: '请选择闲置起始日期', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="idleInfo.startDate"
                    placeholder="请选择闲置起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledStartDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置结束日期" :rules="[{ validator: validateIdleEndDate, trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="idleInfo.endDate"
                    placeholder="请选择闲置结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledEndDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置天数">
                  <span>{{ idleDays }} 天</span>
                  <div class="help-text">空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="空置闲置面积(㎡)">
                  <a-input v-model:value="idleInfo.idleArea" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置资产原值(万元)">
                  <a-input-number
                    v-model:value="idleInfo.originalValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入闲置资产原值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置资产账面价值(万元)" :rules="[{ required: true, message: '请输入闲置资产账面价值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="idleInfo.bookValue"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    placeholder="请输入闲置资产账面价值"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="账面价值时点" :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="idleInfo.bookValueDate"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item label="闲置原因" :rules="[{ required: true, message: '请输入闲置原因', trigger: 'blur' }]">
                  <a-textarea v-model:value="idleInfo.reason" :rows="2" placeholder="请输入闲置原因" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="备注">
                  <a-textarea v-model:value="idleInfo.remark" :rows="2" placeholder="请输入备注" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
          <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="LandForm" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Icon } from '/@/components/Icon';
  import { JDictSelectTag, JAreaLinkage } from '/@/components/Form';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { saveOrUpdate, getDetail } from './land.api';
  import dayjs from 'dayjs';

  // 定义闲置信息接口
  interface IdleInfo {
    startDate: string;
    endDate: string;
    originalValue: number;
    bookValue: number;
    bookValueDate: string;
    reason: string;
    remark: string;
    idleArea: number;
  }

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  // 表单引用
  const formRef = ref();

  // 表单数据
  const formData = ref<any>({
    // 基本信息
    id: '',
    code: '',
    enterpriseCode: '',
    name: '',
    groupName: 0,
    companyName: '',
    ownUnit: '',
    region: '',
    address: '',
    status: 0,
    manageUnit: '',
    reportOrNot: 0,
    operator: '',
    entryClerk: '',
    createTime: '',
    // 土地信息
    useType: 0,
    useTypeInput: '',
    construction: 0,
    gainDate: '',
    gainType: 0,
    landPrice: 0,
    landArrears: 0,
    landArea: 0,
    rentableArea: 0,
    propertyArea: 0,
    notPropertyArea: 0,
    assetsAmount: 0,
    bookAmount: 0,
    dateOfBookValue: '',
    propertyType: 0,
    warrantDate: '',
    custodyEntrustingParty: '',
    warrantIntegration: 0,
    landWarrant: '',
    offAccount: 0,
    insuranceOrNot: 0,
    mortgageOrNot: 0,
    completionOrNot: 0,
    owingOrNot: 0,
    vitalizeOrNot: 0,
    workProgress: '',
    problems: '',
    // 资产使用情况
    assetsStatus: [],
    idleArea: 0,
    useArea: 0,
    rentArea: 0,
    lendArea: 0,
    occupyArea: 0,
    sellArea: 0,
    otherArea: 0,
    remark: '',
  });

  // 闲置信息相关
  const showIdleInfo = ref(false);
  const idleInfo = ref<IdleInfo>({
    startDate: '',
    endDate: '',
    originalValue: 0,
    bookValue: 0,
    bookValueDate: '',
    reason: '',
    remark: '',
    idleArea: 0,
  });

  // 计算闲置天数
  const idleDays = computed(() => {
    if (idleInfo.value.startDate && idleInfo.value.endDate) {
      const start = dayjs(idleInfo.value.startDate);
      const end = dayjs(idleInfo.value.endDate);
      const days = end.diff(start, 'day');
      return days > 0 ? days : 0;
    }
    return 0;
  });

  // 日期禁用函数
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  const disabledStartDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  const disabledEndDate = (current: dayjs.Dayjs) => {
    if (!idleInfo.value.startDate) return false;
    return current && current <= dayjs(idleInfo.value.startDate);
  };

  // 闲置结束日期验证
  const validateIdleEndDate = (rule: any, value: string) => {
    if (value && idleInfo.value.startDate) {
      if (dayjs(value).isSame(dayjs(idleInfo.value.startDate)) || dayjs(value).isBefore(dayjs(idleInfo.value.startDate))) {
        return Promise.reject('闲置结束日期应大于起始日期');
      }
    }
    return Promise.resolve();
  };

  /**
   * 初始化
   */
  onMounted(async () => {
    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadDetail();
    } else {
      isUpdate.value = false;
      // 设置默认值
      Object.assign(formData.value, {
        reportOrNot: 0,
        status: 0,
        groupName: 0,
        insuranceOrNot: 0,
        useType: 0,
        construction: 0,
        gainType: 0,
        propertyType: 0,
        warrantIntegration: 0,
        offAccount: 0,
        mortgageOrNot: 0,
        vitalizeOrNot: 0,
        assetsStatus: [],
      });
    }
  });

  /**
   * 加载详情
   */
  async function loadDetail() {
    try {
      const data = await getDetail(recordId.value);
      if (data) {
        Object.assign(formData.value, data);

        // 处理闲置信息
        if (data.assetsStatus && data.assetsStatus.includes(0)) {
          showIdleInfo.value = true;
          idleInfo.value = {
            startDate: data.idleStartDate || '',
            endDate: data.idleEndDate || '',
            originalValue: data.idleOriginalValue || 0,
            bookValue: data.idleBookValue || 0,
            bookValueDate: data.idleBookValueDate || '',
            reason: data.idleReason || '',
            remark: data.idleRemark || '',
            idleArea: data.idleArea || 0,
          };
        }
      }
    } catch (error) {
      createMessage.error('加载详情失败');
    }
  }

  /**
   * 提交表单
   */
  async function handleSubmit() {
    try {
      // 验证表单
      await formRef.value.validate();

      // 验证闲置信息
      if (showIdleInfo.value) {
        if (!idleInfo.value.startDate) {
          createMessage.error('请填写闲置起始日期');
          return;
        }
        if (!idleInfo.value.bookValue) {
          createMessage.error('请填写闲置资产账面价值');
          return;
        }
        if (!idleInfo.value.bookValueDate) {
          createMessage.error('请选择账面价值时点');
          return;
        }
        if (!idleInfo.value.reason) {
          createMessage.error('请填写闲置原因');
          return;
        }
      }

      // 合并表单数据
      const submitData = {
        ...formData.value,
        // 闲置信息
        idleStartDate: idleInfo.value.startDate,
        idleEndDate: idleInfo.value.endDate,
        idleOriginalValue: idleInfo.value.originalValue,
        idleBookValue: idleInfo.value.bookValue,
        idleBookValueDate: idleInfo.value.bookValueDate,
        idleReason: idleInfo.value.reason,
        idleRemark: idleInfo.value.remark,
        idleArea: idleInfo.value.idleArea,
      };

      loading.value = true;
      await saveOrUpdate(submitData, isUpdate.value);

      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');
      router.push('/assetsInfo/land');
    } catch (error) {
      createMessage.error(isUpdate.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重置表单
   */
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '是否确认重置表单数据？',
      iconType: 'warning',
      onOk: () => {
        if (isUpdate.value) {
          loadDetail();
        } else {
          // 重置表单数据
          Object.assign(formData.value, {
            reportOrNot: 0,
            status: 0,
            groupName: 0,
            insuranceOrNot: 0,
            useType: 0,
            construction: 0,
            gainType: 0,
            propertyType: 0,
            warrantIntegration: 0,
            offAccount: 0,
            mortgageOrNot: 0,
            vitalizeOrNot: 0,
            assetsStatus: [],
          });
          showIdleInfo.value = false;
          idleInfo.value = {
            startDate: '',
            endDate: '',
            originalValue: 0,
            bookValue: 0,
            bookValueDate: '',
            reason: '',
            remark: '',
            idleArea: 0,
          };
        }
      },
    });
  }

  // 监听资产使用状态变化
  watch(
    () => formData.value.assetsStatus,
    (newStatus) => {
      if (newStatus && newStatus.includes(0)) {
        showIdleInfo.value = true;
        // 自动带出数据
        idleInfo.value.originalValue = formData.value.assetsAmount || 0;
        idleInfo.value.bookValue = formData.value.bookAmount || 0;
        idleInfo.value.bookValueDate = formData.value.dateOfBookValue || '';
        idleInfo.value.idleArea = formData.value.idleArea || 0;
      } else {
        showIdleInfo.value = false;
      }
    }
  );
</script>

<style lang="less" scoped>
  .land-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .form-card-action {
          .ant-btn {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    .empty-text {
      color: #909399;
      text-align: center;
      padding: 20px 0;
    }

    // 统一设置表单项标签宽度
    :deep(.ant-form-item-label) {
      width: 180px;
      min-width: 180px;
      text-align: right;
      padding-right: 8px;
    }

    :deep(.ant-form-item-label > label) {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>